# Certificate System Refactor Summary

## Overview
The certificate system has been refactored to support multiple certificates per level with unlimited retakes while maintaining only the 3 most recent certificates per level.

## Key Changes

### 1. Data Structure Changes

#### Updated `CertificateData` Interface
```typescript
export interface CertificateData {
  certificateId: string;
  certificateUrlPageOne: string;
  certificateUrlPageTwo: string;
  userId: string;
  level: string;
  generatedAt: string;
  attemptId?: string;                    // NEW: Optional attempt tracking
  scores?: {                             // NEW: Store actual scores
    pronunciation: number;
    listening: number;
    speaking: number;
  };
  predicates?: {                         // NEW: Store score predicates
    pronunciation: string;
    listening: string;
    speaking: string;
  };
}
```

**Removed Fields:**
- `lastCertificateAttemptId` - No longer needed since unlimited retakes are allowed

**Added Fields:**
- `attemptId` - Optional field to track specific attempts
- `scores` - Stores the actual pronunciation, listening, and speaking scores
- `predicates` - Stores the calculated predicates for each score

### 2. Certificate Generation Logic (`generateCertificate.ts`)

#### Removed Attempt ID Restrictions
- **Before**: Certificate generation was blocked if `currentAttemptId` matched `lastCertificateAttemptId`
- **After**: Certificate is generated every time a level is completed with sufficient scores (≥50 for all skills)

#### Enhanced Data Storage
- Certificates now store complete score information and predicates
- All certificate data is preserved for verification and historical tracking

#### Key Behavior Changes:
- Users can retake levels unlimited times
- Each successful completion (with scores ≥50) generates a new certificate
- No duplicate prevention based on attempt IDs

### 3. Certificate Management Logic (`updateUserCertificate.ts`)

#### Enhanced Certificate Document Creation
- Stores all available certificate data including scores and predicates
- Maintains backward compatibility with existing certificates

#### Automatic Cleanup System
- **Limit**: Maximum 3 certificates per level per user
- **Strategy**: Keep the 3 most recent certificates (ordered by `generatedAt`)
- **Cleanup**: Automatically deletes both Firestore documents and Storage files for old certificates

#### Storage Structure
```
user-data/{userId}/certificates/{certificateId}
├── certificateUrlPageOne
├── certificateUrlPageTwo
├── level
├── generatedAt
├── attemptId (optional)
├── scores (optional)
└── predicates (optional)
```

## User Experience Changes

### Before Refactor
- Users could only get one certificate per level per unique attempt
- Retaking a level with the same attempt ID would not generate a new certificate
- Limited certificate history

### After Refactor
- Users can retake any level unlimited times
- Each successful completion generates a new certificate
- System maintains the 3 most recent certificates per level
- Complete score history is preserved in certificates
- Automatic cleanup prevents storage bloat

## Technical Benefits

1. **Simplified Logic**: Removed complex attempt ID tracking
2. **Better Data Integrity**: Complete score information stored with each certificate
3. **Automatic Cleanup**: Prevents unlimited storage growth
4. **Flexible Retakes**: Users can improve their scores without restrictions
5. **Historical Tracking**: Maintains recent certificate history for verification

## Database Impact

### Firestore Collections
```
user-data/{userId}/certificates/
├── {certificateId1} (most recent)
├── {certificateId2} (second most recent)
└── {certificateId3} (third most recent)
```

### Storage Structure
```
certificates/{userId}/{level}/
├── {certificateId1}-01.svg
├── {certificateId1}-02.svg
├── {certificateId2}-01.svg
├── {certificateId2}-02.svg
├── {certificateId3}-01.svg
└── {certificateId3}-02.svg
```

## Migration Notes

- Existing certificates remain unchanged and functional
- New certificates will include the enhanced data structure
- No breaking changes to existing certificate verification systems
- The system gracefully handles certificates with or without the new optional fields

## Monitoring and Logging

Enhanced logging includes:
- Certificate generation attempts and outcomes
- Cleanup operations (which certificates were removed)
- Score validation results
- Storage operations (upload/delete)

This refactor provides a more flexible and user-friendly certificate system while maintaining data integrity and preventing storage bloat.