import {getAuth} from "firebase-admin/auth";
import {getFirestore} from "firebase-admin/firestore";
import {onCall, HttpsError} from "firebase-functions/v2/https";

export const reinitUserDataFunction = onCall(
  {region: "asia-southeast1", enforceAppCheck: true}, async (request) => {
    if (!request.auth) {
      throw new HttpsError(
        "unauthenticated",
        "The function must be called while authenticated."
      );
    }

    const user = await getAuth().getUser(request.auth.uid);

    const db = getFirestore();
    const userDataDoc = db.collection("user-data").doc(request.auth.uid);
    const userData = await userDataDoc.get();
    if (!userData.exists) {
      await userDataDoc.set({
        email: user.email,
        is_after_test: false,
        last_course: null,
      });
    }
  });
