/* eslint-disable require-jsdoc */
import {onC<PERSON>, HttpsError} from "firebase-functions/v2/https";
import {getFirestore, CollectionReference} from "firebase-admin/firestore";
import {logger} from "firebase-functions/v2";
import {ulid} from "ulid";

/**
 * Helper function to delete all documents in a collection
 * @param {FirebaseFirestore.Firestore} db - Firestore instance
 * @param {CollectionReference} collectionRef - Collection reference to delete
 * @return {Promise<void>}
 */
async function deleteCollectionDocuments(
  db: FirebaseFirestore.Firestore,
  collectionRef: CollectionReference
): Promise<void> {
  const snapshot = await collectionRef.get();

  if (snapshot.empty) {
    return;
  }

  // Process deletions in batches of 500 (Firestore batch limit)
  const batchSize = 500;
  const docs = snapshot.docs;

  for (let i = 0; i < docs.length; i += batchSize) {
    const batch = db.batch();
    const batchDocs = docs.slice(i, i + batchSize);

    batchDocs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
  }
}

export const resetLevelProgress = onCall(
  {region: "asia-southeast1", enforceAppCheck: true},
  async (request) => {
    if (!request.app) {
      throw new HttpsError(
        "failed-precondition",
        "The function must be called from an App Check verified app.",
      );
    }

    if (!request.auth) {
      throw new HttpsError(
        "unauthenticated",
        "The function must be called while authenticated.",
      );
    }

    const {levelId} = request.data;
    const userId = request.auth.uid;

    if (!levelId) {
      throw new HttpsError(
        "invalid-argument",
        "levelId is required.",
      );
    }

    if (typeof levelId !== "string") {
      throw new HttpsError(
        "invalid-argument",
        "levelId must be a string.",
      );
    }

    try {
      const db = getFirestore();
      const levelDocRef = db.doc(`user-data/${userId}/lessons/${levelId}`);
      const levelDoc = await levelDocRef.get();

      if (!levelDoc.exists) {
        throw new HttpsError(
          "not-found",
          "Level not found for this user.",
        );
      }

      const levelData = levelDoc.data();

      // Check if user has any certificate for this level
      // User can only reset if they already have a certificate
      const hasCertificate = levelData?.certificateId ||
                           levelData?.certificateUrl ||
                           levelData?.lastCertificateAttemptId;

      if (!hasCertificate) {
        throw new HttpsError(
          "failed-precondition",
          "Cannot reset progress: No certificate found for this level. " +
          "You must complete the level and earn a certificate before " +
          "resetting.",
        );
      }

      // Generate new attempt ID
      const newAttemptId = ulid();
      const currentTime = new Date();

      // Reset lesson progress data while keeping certificate information
      const resetData = {
        // Reset progress fields
        isComplete: false,
        result: null,
        updatedAt: currentTime,

        // Set new attempt tracking
        attemptId: newAttemptId,
        lastResetAt: currentTime,

        // Keep certificate data intact
        // certificateId, certificateUrl, lastCertificateAttemptId remain
        // unchanged
      };

      // Get all chapters in this level
      const chaptersRef = db.collection(
        `user-data/${userId}/lessons/${levelId}/chapters`
      );
      const chaptersSnapshot = await chaptersRef.get();

      // Define section types
      const sectionTypes = [
        "pronunciation",
        "conversation",
        "listening",
        "speaking",
      ];

      // Prepare all operations
      const operations = [];

      // 1. Reset the main level document
      operations.push(levelDocRef.set(resetData, {merge: true}));

      if (!chaptersSnapshot.empty) {
        // 2. Reset all chapters and collect section deletion operations
        const chapterResetBatch = db.batch();
        const sectionDeletionPromises = [];

        for (const chapterDoc of chaptersSnapshot.docs) {
          const chapterId = chapterDoc.id;

          // Reset chapter document
          chapterResetBatch.set(chapterDoc.ref, {
            isComplete: false,
            result: null,
            updatedAt: currentTime,
          }, {merge: true});

          // For each section type, delete all results documents
          for (const sectionType of sectionTypes) {
            const resultsRef = db.collection(
              `user-data/${userId}/lessons/${levelId}/chapters/` +
              `${chapterId}/sections/${sectionType}/results`
            );

            // Add deletion operation for this section's results
            sectionDeletionPromises.push(
              deleteCollectionDocuments(db, resultsRef)
            );
          }
        }

        // Add chapter reset batch operation
        operations.push(chapterResetBatch.commit());

        // Add all section deletion operations
        operations.push(...sectionDeletionPromises);
      }

      // Execute all operations in parallel for better performance
      await Promise.all(operations);

      const chaptersCount = chaptersSnapshot.empty ?
        0 : chaptersSnapshot.docs.length;
      const sectionsCount = chaptersCount * sectionTypes.length;

      logger.log(
        `Level progress reset successfully for user ${userId}, ` +
        `level ${levelId}, new attemptId: ${newAttemptId}. ` +
        `Reset ${chaptersCount} chapters and deleted results from ` +
        `${sectionsCount} sections.`
      );

      return {
        success: true,
        message: "Level progress has been reset successfully.",
        attemptId: newAttemptId,
      };
    } catch (error) {
      if (error instanceof HttpsError) {
        throw error;
      }

      logger.error(
        `Error resetting level progress for user ${userId}, level ${levelId}:`,
        error
      );

      throw new HttpsError(
        "internal",
        "An error occurred while resetting level progress.",
      );
    }
  },
);
