/* eslint-disable require-jsdoc */
import {onDocumentWritten} from "firebase-functions/v2/firestore";
import {getFirestore} from "firebase-admin/firestore";
import {getStorage} from "firebase-admin/storage";
import {logger} from "firebase-functions/v2";
import {CertificateData} from "./interface/CertificateData";

export const updateUserCertificate = onDocumentWritten(
  {
    document: "user-data/{userId}/lessons/{levelId}",
    region: "asia-southeast1",
  },
  async (event) => {
    const beforeData = event.data?.before.data();
    const afterData = event.data?.after.data();

    // Check if certificate data was added
    // (certificateId exists in after but not before)
    if (beforeData?.certificateId || !afterData?.certificateId) {
      logger.log("No new certificate data added, exiting.");
      return;
    }

    const {userId} = event.params;
    const {
      certificateId,
      certificateUrlPageOne,
      certificateUrlPageTwo,
      level,
      generatedAt,
      attemptId,
      scores,
      predicates,
    } = afterData;

    if (!certificateId || !certificateUrlPageOne || !certificateUrlPageTwo ||
        !level || !generatedAt) {
      logger.error("Missing required certificate data", {
        certificateId,
        certificateUrlPageOne,
        certificateUrlPageTwo,
        level,
        generatedAt,
      });
      return;
    }

    const db = getFirestore();
    const certificatesRef = db.collection(`user-data/${userId}/certificates`);

    try {
      // Create the certificate document with certificateId as document ID
      const certificateDocData: Partial<CertificateData> = {
        certificateUrlPageOne: certificateUrlPageOne,
        certificateUrlPageTwo: certificateUrlPageTwo,
        level: level,
        generatedAt: generatedAt,
      };

      // Include optional fields if they exist
      if (attemptId !== undefined) {
        certificateDocData.attemptId = attemptId;
      }
      if (scores !== undefined) {
        certificateDocData.scores = scores;
      }
      if (predicates !== undefined) {
        certificateDocData.predicates = predicates;
      }

      await certificatesRef.doc(certificateId).set(certificateDocData);

      logger.log(
        `Certificate document created for user ${userId}, ` +
          `certificateId: ${certificateId}, level: ${level}`,
      );

      // Check if there are more than 3 certificates for the current level
      // Keep only the 3 most recent certificates per level
      const levelCertificatesQuery = await certificatesRef
        .where("level", "==", level)
        .orderBy("generatedAt", "asc")
        .get();

      if (levelCertificatesQuery.size > 3) {
        const certificatesToDelete = levelCertificatesQuery.docs.slice(
          0,
          levelCertificatesQuery.size - 3,
        );

        const bucket = getStorage().bucket();

        for (const certDoc of certificatesToDelete) {
          const oldCertificateId = certDoc.id;

          try {
            // Delete both certificate files from storage
            const fileNamePageOne = `${oldCertificateId}-01.svg`;
            const fileNamePageTwo = `${oldCertificateId}-02.svg`;
            const filePathPageOne =
              `certificates/${userId}/${level}/${fileNamePageOne}`;
            const filePathPageTwo =
              `certificates/${userId}/${level}/${fileNamePageTwo}`;

            // Delete both files in parallel
            await Promise.all([
              bucket.file(filePathPageOne).delete(),
              bucket.file(filePathPageTwo).delete(),
            ]);

            logger.log(
              `Deleted certificate files: ${filePathPageOne}, ` +
                `${filePathPageTwo}`,
            );

            // Delete the certificate document
            await certDoc.ref.delete();
            logger.log(`Deleted certificate document: ${oldCertificateId}`);
          } catch (deleteError) {
            logger.error(
              `Failed to delete certificate ${oldCertificateId}`,
              deleteError,
            );
          }
        }

        logger.log(
          `Cleaned up ${certificatesToDelete.length} old certificates ` +
            `for user ${userId}, level ${level}`,
        );
      }
    } catch (error) {
      logger.error(
        `Failed to update user certificate for ${userId}, ` +
          `certificateId: ${certificateId}`,
        error,
      );
    }
  },
);
