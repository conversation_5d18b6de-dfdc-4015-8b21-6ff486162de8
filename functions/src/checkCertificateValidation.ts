/* eslint-disable require-jsdoc */
import {on<PERSON><PERSON>, HttpsError} from "firebase-functions/v2/https";
import {getFirestore} from "firebase-admin/firestore";

export const checkCertificateValidation = onCall(
  {region: "asia-southeast1", enforceAppCheck: true},
  async (request) => {
    if (!request.app) {
      throw new HttpsError(
        "failed-precondition",
        "The function must be called from an App Check verified app.",
      );
    }

    const {userid, certid} = request.data;

    if (!userid || !certid) {
      throw new HttpsError(
        "invalid-argument",
        "Both userid and certid are required parameters.",
      );
    }

    if (typeof userid !== "string" || typeof certid !== "string") {
      throw new HttpsError(
        "invalid-argument",
        "Both userid and certid must be strings.",
      );
    }

    try {
      const db = getFirestore();
      const certificateDocRef = db
        .collection("user-data")
        .doc(userid)
        .collection("certificates")
        .doc(certid);

      const certificateDoc = await certificateDocRef.get();

      if (certificateDoc.exists) {
        const certificateData = certificateDoc.data();
        return {
          status: "valid",
          message: "Certificate is valid",
          certificate: certificateData,
        };
      } else {
        throw new HttpsError(
          "not-found",
          "Certificate is invalid - document does not exist",
        );
      }
    } catch (error) {
      if (error instanceof HttpsError) {
        throw error;
      }

      console.error("Error checking certificate validation:", error);
      throw new HttpsError(
        "internal",
        "An error occurred while validating the certificate",
      );
    }
  },
);
