/* eslint-disable require-jsdoc */
import {defineString} from "firebase-functions/params";
import {logger} from "firebase-functions/v2";
import {HttpsError, onCall} from "firebase-functions/v2/https";
import {
  AudioConfig,
  AudioInputStream,
  PronunciationAssessmentConfig,
  PronunciationAssessmentGradingSystem,
  PronunciationAssessmentGranularity,
  PronunciationAssessmentResult,
  PushAudioInputStream,
  SpeechConfig, SpeechRecognizer,
} from "microsoft-cognitiveservices-speech-sdk";
import {
  PronunciationAssessmentResultData,
} from "./interface/PronunciationAssessmentResultData";
import {
  FormattedResult, PronunciationCheckFormatedResult,
} from "./class/PronunciationCheckFormatedResult";

const subscriptionKey = defineString("SUBSCRIPTION_KEY");
const serviceRegion = defineString("SERVICE_REGION");

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function validateAudioData(audioData: any): void {
  if (!audioData) {
    throw new HttpsError("invalid-argument", "No audio data provided");
  }

  if (typeof audioData !== "string") {
    throw new HttpsError("invalid-argument", "Audio data must be a string");
  }

  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  if (!base64Regex.test(audioData)) {
    throw new HttpsError(
      "invalid-argument",
      "Invalid base64 encoded audio data"
    );
  }
}

function validateText(text: unknown): void {
  if (!text) {
    throw new HttpsError("invalid-argument", "No text provided");
  }

  if (typeof text !== "string") {
    throw new HttpsError("invalid-argument", "Text must be a string");
  }

  if (text.trim().length === 0) {
    throw new HttpsError("invalid-argument", "Text cannot be empty");
  }

  if (text.length > 1000) {
    throw new HttpsError(
      "invalid-argument",
      "Text exceeds maximum length of 1000 characters"
    );
  }

  // const validTextRegex = /^[a-zA-Z0-9\s.,!?'"-]+$/;
  // if (!validTextRegex.test(text)) {
  //   throw new HttpsError(
  //     "invalid-argument",
  //     "Text contains invalid characters. " +
  //     "Only letters, numbers, spaces and basic punctuation are allowed"
  //   );
  // }
}
function validateWavFormat(buffer: Buffer): void {
  if (buffer.length < 44) {
    throw new HttpsError(
      "invalid-argument",
      "Invalid WAV file: file too small"
    );
  }

  if (buffer.toString("ascii", 0, 4) !== "RIFF") {
    throw new HttpsError(
      "invalid-argument",
      "Invalid WAV file: missing RIFF header"
    );
  }

  if (buffer.toString("ascii", 8, 12) !== "WAVE") {
    throw new HttpsError(
      "invalid-argument",
      "Invalid WAV file: not a WAVE format"
    );
  }

  // logger.info("buffer.readInt16LE(20):", buffer.readInt16LE(20));
  // if (buffer.readInt16LE(20) !== 1) {
  //   throw new HttpsError(
  //     "invalid-argument",
  //     "Invalid WAV file: not PCM format"
  //   );
  // }

  // const channels = buffer.readInt16LE(22);
  // if (channels !== 1 && channels !== 2) {
  //   throw new HttpsError(
  //     "invalid-argument",
  //     "Invalid WAV file: must be mono or stereo audio"
  //   );
  // }

  // const sampleRate = buffer.readInt32LE(24);
  // if (sampleRate !== 8000 && sampleRate !== 16000) {
  //   throw new HttpsError(
  //     "invalid-argument",
  //     "Invalid WAV file: sample rate must be 8 kHz or 16 kHz"
  //   );
  // }

  // const bitsPerSample = buffer.readInt16LE(34);
  // if (bitsPerSample !== 16) {
  //   throw new HttpsError(
  //     "invalid-argument",
  //     "Invalid WAV file: bit depth must be 16 bits"
  //   );
  // }
}
export const checkFromBuffer = onCall(
  {region: "asia-southeast1", enforceAppCheck: true},
  async (request) => {
    const {text, audioData} = request.data;

    try {
      validateText(text);
      validateAudioData(audioData);
      const buffer = Buffer.from(audioData, "base64");
      validateWavFormat(buffer);

      const pushStream = AudioInputStream.createPushStream();
      pushStream.write(buffer);
      pushStream.close();

      const result = await performPronunciationAssessment(
        pushStream, text
      );
      return result;
    } catch (error) {
      logger.error("Error in pronunciation assessment:", error);
      throw new HttpsError(
        "internal",
        error instanceof HttpsError ?
          error.message : "ERROR_DURING_PRONUNCIATION_ASSESSMENT"
      );
    }
  });

async function performPronunciationAssessment(
  stream: PushAudioInputStream,
  referenceText: string,
): Promise<PronunciationCheckFormatedResult> {
  const speechConfig = SpeechConfig
    .fromSubscription(
      subscriptionKey.value(),
      serviceRegion.value()
    );

  const audioConfig = AudioConfig.fromStreamInput(stream);
  const pronunciationAssessmentConfig = new PronunciationAssessmentConfig(
    referenceText,
    PronunciationAssessmentGradingSystem.HundredMark,
    PronunciationAssessmentGranularity.Phoneme,
    true
  );

  pronunciationAssessmentConfig.enableProsodyAssessment = true;

  const recognizer = new SpeechRecognizer(speechConfig, audioConfig);
  pronunciationAssessmentConfig.applyTo(recognizer);

  return new Promise((resolve, reject) => {
    try {
      recognizer.recognizeOnceAsync(
        (result) => {
          try {
            const pronunciationAssessmentResult = PronunciationAssessmentResult
              .fromResult(result);
            const formattedResult = formatResult(pronunciationAssessmentResult);
            logger.info("FORMATTED_RESULT:", JSON.stringify(formattedResult));
            resolve(formattedResult);
          } catch (error) {
            reject(new HttpsError("internal", String(error)));
          } finally {
            recognizer.close();
            speechConfig.close();
          }
        }, (error) => {
          reject(new HttpsError("internal", String(error)));
          recognizer.close();
          speechConfig.close();
        }
      );
    } catch (error) {
      recognizer.close();
      speechConfig.close();
      audioConfig.close();
      reject(new HttpsError("internal", String(error)));
    }
  });
}

function formatResult(
  data: PronunciationAssessmentResult
): PronunciationCheckFormatedResult {
  const resultData: PronunciationAssessmentResultData = JSON.parse(
    JSON.stringify(
      data
    ));

  const result = new PronunciationCheckFormatedResult();

  result.originalText = "";
  result.recordedInput = resultData.privPronJson.Display;
  result.accuracyScore = resultData.privPronJson
    .PronunciationAssessment.AccuracyScore;
  result.fluencyScore = resultData.privPronJson
    .PronunciationAssessment.FluencyScore;
  result.prosodyScore = resultData.privPronJson
    .PronunciationAssessment.ProsodyScore;
  result.completenessScore = resultData.privPronJson
    .PronunciationAssessment.CompletenessScore;
  result.pronScore = resultData.privPronJson
    .PronunciationAssessment.PronScore;
  result.formattedResult = [];

  resultData.privPronJson.Words.forEach((word, wordIndex) => {
    /**
    * Give space between words.
    */
    if (wordIndex > 0) {
      const formattedResult = new FormattedResult();
      formattedResult.text = " ";
      formattedResult.accuracyScore = 100;
      formattedResult.errorType = "None";
      result.formattedResult.push(formattedResult);
    }

    if (word.Syllables) {
      let hasGrapheme = false;
      let noGraphemeCount = 0;
      let noGraphemeScore = 0;
      word.Syllables.forEach((syllable, syllableIndex) => {
        if (syllable.Grapheme) {
          hasGrapheme = true;
          const formattedResult = new FormattedResult();
          if (wordIndex === 0 && syllableIndex === 0) {
            /** Format Titlecase for the first word */
            formattedResult.text =
                syllable.Grapheme.charAt(0).toUpperCase() +
                syllable.Grapheme.slice(1);
          } else {
            formattedResult.text = syllable.Grapheme;
          }

          formattedResult.accuracyScore = syllable.
            PronunciationAssessment.AccuracyScore;
          formattedResult.errorType = word.PronunciationAssessment.ErrorType;
          result.formattedResult.push(formattedResult);
        } else {
          hasGrapheme = false;
          noGraphemeCount++;
          noGraphemeScore += syllable.PronunciationAssessment.AccuracyScore;
        }
      });

      if (!hasGrapheme) {
        const formattedResult = new FormattedResult();
        if (wordIndex === 0 ) {
          /** Format Titlecase for the first word */
          formattedResult.text =
              word.Word.charAt(0).toUpperCase() +
              word.Word.slice(1);
        } else {
          formattedResult.text = word.Word;
        }
        formattedResult.accuracyScore = noGraphemeScore / noGraphemeCount;
        formattedResult.errorType = word.PronunciationAssessment.ErrorType;
        result.formattedResult.push(formattedResult);
      }
    } else {
      const formattedResult = new FormattedResult();
      if (wordIndex === 0) {
        /** Format Titlecase for the first word */
        formattedResult.text = word.Word.charAt(0).toUpperCase() +
          word.Word.slice(1);
      } else {
        formattedResult.text = word.Word;
      }
      formattedResult.accuracyScore = 0;
      formattedResult.errorType = word.PronunciationAssessment.ErrorType;
      result.formattedResult.push(formattedResult);
    }
  });

  return result;
}
