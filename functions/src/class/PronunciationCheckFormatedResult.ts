/**
 * Represents the formatted result of a pronunciation check.
 */
export class PronunciationCheckFormatedResult {
  public originalText!: string;
  public recordedInput!: string;
  public accuracyScore!: number;
  public fluencyScore!: number;
  public prosodyScore!: number;
  public completenessScore!: number;
  public pronScore!: number;
  public formattedResult!: FormattedResult[];
}

/**
 * Data structure for the formatted result.
 */
export class FormattedResult {
  public text!: string;
  public accuracyScore!: number;
  public errorType!: string;
}
