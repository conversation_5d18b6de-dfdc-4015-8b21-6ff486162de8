import {initializeApp} from "firebase-admin/app";
initializeApp();

import {initUserDataFunction} from "./initUserData";
import {checkFromBuffer} from "./checkFromBufferFile";
import {reinitUserDataFunction} from "./reinitUserData";
import {setChapterComplete} from "./setChapterComplete";
import {setLevelComplete} from "./setLevelComplete";
import {generateCertificate} from "./generateCertificate";
import {updateUserCertificate} from "./updateUserCertificate";
import {checkCertificateValidation} from "./checkCertificateValidation";
import {resetLevelProgress} from "./resetLevelProgress";

export const initUserData = initUserDataFunction;
export const reinitUserData = reinitUserDataFunction;
export const checkPronunciationViaBuffer = checkFromBuffer;
export const chapterComplete = setChapterComplete;
export const levelComplete = setLevelComplete;
export const genCertificate = generateCertificate;
export const updateCertificate = updateUserCertificate;
export const checkCertificate = checkCertificateValidation;
export const resetLevel = resetLevelProgress;
