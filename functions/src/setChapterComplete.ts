/* eslint-disable require-jsdoc */
import {onDocumentWritten} from "firebase-functions/v2/firestore";
import {getFirestore, FieldValue} from "firebase-admin/firestore";


async function calculateResult(userId: string, levelId: string, chId: string) {
  const db = getFirestore();
  const sections = ["pronunciation", "listening", "speaking"];

  const results: {[key: string]: number} = {};

  // Process all sections in parallel for better performance
  const sectionPromises = sections.map(async (section) => {
    const resultsCollection = db.collection(
      `user-data/${userId}/lessons/${levelId}/chapters/${chId}/sections/` +
      `${section}/results`
    );
    const resultsSnapshot = await resultsCollection.get();

    if (resultsSnapshot.empty) {
      return {section, score: 0};
    }

    const resultDocs = resultsSnapshot.docs.map((doc) => doc.data());

    let score = 0;

    if (section === "pronunciation") {
      // Calculate average of result.pronScore
      const pronScores = resultDocs
        .map((doc) => doc.result?.pronScore)
        .filter((score) => typeof score === "number");

      score = pronScores.length > 0 ?
        pronScores.reduce((sum, score) => sum + score, 0) / pronScores.length :
        0;
    } else if (section === "listening") {
      // Calculate percentage score from correct/total answers
      const validResults = resultDocs
        .filter((doc) =>
          typeof doc.result?.correct === "number" &&
          typeof doc.result?.total === "number" &&
          doc.result.total > 0
        );

      if (validResults.length === 0) {
        score = 0;
      } else {
        // Calculate percentage for each result, then average
        const percentages = validResults.map((doc) =>
          (doc.result.correct / doc.result.total) * 100
        );
        score = percentages.reduce((sum, percentage) => sum + percentage, 0) /
          percentages.length;
      }
    } else if (section === "speaking") {
      // Calculate average of result.pronScore for stage2 and stage3
      const stage2And3Results = resultDocs.filter((doc) =>
        doc.speakingStage === "stage2" || doc.speakingStage === "stage3"
      );

      const pronScores = stage2And3Results
        .map((doc) => doc.result?.pronScore)
        .filter((score) => typeof score === "number");

      score = pronScores.length > 0 ?
        pronScores.reduce((sum, score) => sum + score, 0) / pronScores.length :
        0;
    }

    return {section, score};
  });

  // Wait for all section calculations to complete
  const sectionResults = await Promise.all(sectionPromises);

  // Build results object
  sectionResults.forEach(({section, score}) => {
    results[section] = score;
  });

  return results;
}

async function setComplete(
  userId: string,
  levelId: string,
  chId: string,
): Promise<void> {
  const db = getFirestore();

  // Calculate results and update completion status in parallel
  const [results] = await Promise.all([
    calculateResult(userId, levelId, chId),
  ]);

  await db.doc(`user-data/${userId}/lessons/${levelId}/chapters/${chId}`).set(
    {
      isComplete: true,
      updatedAt: FieldValue.serverTimestamp(),
      result: results,
    },
    {merge: true},
  );
}

export const setChapterComplete = onDocumentWritten(
  {
    document: "user-data/{userId}/lessons/{levelId}/chapters/{chId}/" +
      "sections/{sectionId}",
    region: "asia-southeast1",
  },
  async (event) => {
    if (!event.data || !event.data.after.exists) {
      return;
    }

    const userId = event.params.userId;
    const levelId = event.params.levelId;
    const chId = event.params.chId;

    const docData = event.data.after.data();

    // Only proceed if the section was just marked as complete
    if (!docData?.isComplete) {
      return;
    }

    const db = getFirestore();
    const sections = [
      "pronunciation",
      "conversation",
      "listening",
      "speaking",
    ];

    // Event if a chapter already marked as complete, we still need
    // to process it, because there might be changes in lesson results.
    // Check if chapter is already complete to avoid redundant processing
    // const chapterDoc = await db.doc(
    //   `user-data/${userId}/lessons/${levelId}/chapters/${chId}`
    // ).get();

    // if (chapterDoc.exists && chapterDoc.data()?.isComplete) {
    //   return; // Chapter already marked as complete
    // }

    // Get all sections completion status in a single query
    const sectionsSnapshot = await db.collection(
      `user-data/${userId}/lessons/${levelId}/chapters/${chId}/sections`
    ).where("isComplete", "==", true).get();

    const completedSections = new Set(sectionsSnapshot.docs
      .map((doc) => doc.id));

    // Check if all sections are complete
    const allSectionsComplete = sections.every((section) =>
      completedSections.has(section)
    );

    if (allSectionsComplete) {
      await setComplete(userId, levelId, chId);
    }
  });
