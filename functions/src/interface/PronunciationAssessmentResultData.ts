export interface PronunciationAssessmentResultData {
    privPronJson: PrivPronJson
  }

export interface PrivPronJson {
    Confidence: number
    Lexical: string
    ITN: string
    MaskedITN: string
    Display: string
    PronunciationAssessment: PronunciationAssessment
    Words: Word[]
  }

export interface PronunciationAssessment {
    AccuracyScore: number
    FluencyScore: number
    ProsodyScore: number
    CompletenessScore: number
    PronScore: number
  }

export interface Word {
    Word: string
    Offset?: number
    Duration?: number
    PronunciationAssessment: WordPronunciationAssessment
    Syllables?: Syllable[]
    Phonemes: Phoneme[]
  }

export interface WordPronunciationAssessment {
    AccuracyScore?: number
    ErrorType: string
    Feedback?: Feedback
  }

export interface Feedback {
    Prosody: Prosody
  }

export interface Prosody {
    Break: Break
    Intonation: Intonation
  }

export interface Break {
    ErrorTypes: string[]
    UnexpectedBreak?: UnexpectedBreak
    MissingBreak?: MissingBreak
    BreakLength: number
  }

export interface UnexpectedBreak {
    Confidence: number
  }

export interface MissingBreak {
    Confidence: number
  }

export interface Intonation {
    ErrorTypes: unknown[]
    Monotone: Monotone
  }

export interface Monotone {
    SyllablePitchDeltaConfidence: number
  }

export interface Syllable {
    Syllable: string
    Grapheme?: string
    PronunciationAssessment: SyllablePronunciationAssessment
    Offset: number
    Duration: number
  }

export interface SyllablePronunciationAssessment {
    AccuracyScore: number
  }

export interface Phoneme {
    Phoneme: string
    PronunciationAssessment: PronunciationAssessment4
    Offset: number
    Duration: number
  }

export interface PronunciationAssessment4 {
    AccuracyScore: number
  }
