// import {applicationDefault, initializeApp} from "firebase-admin/app";
import {getFirestore} from "firebase-admin/firestore";
import {region} from "firebase-functions/v1";

// initializeApp({credential: applicationDefault()});

export const initUserDataFunction = region("asia-southeast1").auth.user()
  .onCreate((user) => {
    const db = getFirestore();
    return db.collection("user-data").doc(user.uid).set({
      email: user.email,
      is_after_test: false,
      last_course: null,
    });
  });

