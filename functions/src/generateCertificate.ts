/* eslint-disable valid-jsdoc */
/* eslint-disable require-jsdoc */
import {onDocumentWritten} from "firebase-functions/v2/firestore";
import {getStorage} from "firebase-admin/storage";
import {getAuth} from "firebase-admin/auth";
import * as os from "os";
import * as fs from "fs/promises";
import * as path from "path";
import {ulid} from "ulid";
import * as sharp from "sharp";
import {certificateTemplate} from "./template/certificateTemplate";
import {certificateTemplatePageTwo}
  from "./template/certificateTemplatePageTwo";
import {logger} from "firebase-functions/v2";
import * as QRCode from "qrcode-svg";
import {LessonResult} from "./interface/LessonResult";
import {CertificateData} from "./interface/CertificateData";

interface CefrLevel {
  level: string;
  levelName: string;
  description: string;
  whatYouCanDo: string;
  whatYouAchieve: string;
  predicate: PredicateInfo[];
}

interface PredicateInfo {
  minScore: number;
  maxScore: number;
  predicate: string;
}

const cefrLevels: { [key: string]: CefrLevel } = {
  A1: {
    level: "A1",
    levelName: "Beginner",
    description:
      "Able to engage in basic conversations, introduce yourself," +
      " ask simple questions, and make basic plans.",
    whatYouCanDo:
      "You can now engage in basic conversations, introduce" +
      " yourself, ask simple questions, and make basic plans. Keep growing!",
    whatYouAchieve: "You've laid the foundation for communication!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "In Progress",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Developing",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Achieved",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "Outstanding",
      },
    ],
  },
  A2: {
    level: "A2",
    levelName: "Elementary",
    description:
      "Able to discuss hobbies, share past experiences, navigate" +
      " travel situations, and handle social invitations confidently.",
    whatYouCanDo:
      "You can now discuss hobbies, share past experiences, " +
      "navigate travel situations, and handle social invitations confidently.",
    whatYouAchieve: "You've built solid communication skills!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "Needs Support",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Emerging",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Competent",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "Excellent",
      },
    ],
  },
  B1: {
    level: "B1",
    levelName: "Intermediate",
    description:
      "Able to discuss work, studies, relationships, express" +
      " opinions, give advice, and talk about current events with ease.",
    whatYouCanDo:
      "You can now discuss work, studies, relationships, express" +
      " opinions, give advice, and talk about current events with ease.",
    whatYouAchieve: "You've become an independent communicator!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "Below B1",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Basic B1",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Solid B1",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "Strong B1",
      },
    ],
  },
  B2: {
    level: "B2",
    levelName: "Upper-Intermediate",
    description:
      "Able to debate, analyze complex topics, summarize" +
      " information, and improve your presentations.",
    whatYouCanDo:
      "You can now debate, analyze complex topics, summarize" +
      " information, and improve your presentations.",
    whatYouAchieve: "You've reached a high level of fluency!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "Not Yet B2",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Emerging B2",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Competent B2",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "High B2",
      },
    ],
  },
  C1: {
    level: "C1",
    levelName: "Advanced",
    description:
      "Able to lead meetings, write reports, and navigate" +
      " cross-cultural communication with ease.",
    whatYouCanDo:
      "You can now lead meetings, write reports, and navigate" +
      " cross-cultural communication with ease.",
    whatYouAchieve: "You've reached advanced proficiency!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "Below C1",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Minimum C1",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Confident C1",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "Advanced Mastery",
      },
    ],
  },
  C2: {
    level: "C2",
    levelName: "Proficient",
    description:
      "Able to lead debates, analyze complex issues, and deliver" +
      " clear, research-based presentations.",
    whatYouCanDo:
      "You can now lead debates, analyze complex issues, and" +
      " deliver clear, research-based presentations.",
    whatYouAchieve: "You have reached mastery!",
    predicate: [
      {
        minScore: 0,
        maxScore: 49,
        predicate: "Not Yet C2",
      },
      {
        minScore: 50,
        maxScore: 69,
        predicate: "Functional C2",
      },
      {
        minScore: 70,
        maxScore: 84,
        predicate: "Proficient User",
      },
      {
        minScore: 85,
        maxScore: 100,
        predicate: "Near Native",
      },
    ],
  },
};

function wrapText(text: string, maxWidth = 80): string[] {
  const words = text.split(" ");
  const lines: string[] = [];
  let currentLine = "";

  for (const word of words) {
    if ((currentLine + word).length <= maxWidth) {
      currentLine += (currentLine ? " " : "") + word;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  }
  if (currentLine) lines.push(currentLine);
  return lines;
}

function validateScores(result: LessonResult | null | undefined): boolean {
  if (!result || typeof result !== "object") {
    logger.log("No result data found for score validation");
    return false;
  }

  const minScore = 50;
  const skills: Array<keyof LessonResult> = [
    "pronunciation",
    "listening",
    "speaking",
  ];

  for (const skill of skills) {
    const score = result[skill];
    if (typeof score !== "number" || score < minScore) {
      logger.log(
        `Score validation failed: ${skill} score is ${score}, ` +
          `required minimum is ${minScore}`
      );
      return false;
    }
  }

  logger.log(
    `Score validation passed: pronunciation=${result.pronunciation}, ` +
      `listening=${result.listening}, speaking=${result.speaking}`
  );
  return true;
}

function getPredicateForScore(score: number, level: string): string {
  const levelInfo = cefrLevels[level];
  if (!levelInfo) {
    return "Unknown";
  }

  for (const predicate of levelInfo.predicate) {
    if (score >= predicate.minScore && score <= predicate.maxScore) {
      return predicate.predicate;
    }
  }

  return "Unknown";
}

/**
 * Converts SVG content to PNG buffer using Sharp
 */
async function convertSvgToPng(
  svgContent: string,
  width = 1200,
  height = 900,
  quality = 85
): Promise<Buffer> {
  try {
    // Ensure SVG has proper XML declaration and root element
    let processedSvg = svgContent;
    if (!processedSvg.startsWith("<?xml")) {
      processedSvg = `<?xml version="1.0" encoding="UTF-8"?>\n${processedSvg}`;
    }
    if (!processedSvg.includes("<svg")) {
      throw new Error("Invalid SVG content: missing <svg> element");
    }

    const svgBuffer = Buffer.from(processedSvg, "utf-8");

    return await sharp(svgBuffer)
      .png({
        quality,
        compressionLevel: 6,
        progressive: false,
        force: true, // Force PNG output
      })
      .resize(width, height, {
        fit: "contain",
        background: {r: 255, g: 255, b: 255, alpha: 1},
        withoutEnlargement: false,
      })
      .toBuffer();
  } catch (error) {
    logger.error("Failed to convert SVG to PNG:", error);
    throw new Error(`SVG to PNG conversion failed: ${error}`);
  }
}

/**
 * Processes certificate template with data substitution
 */
function processCertificateTemplate(
  template: string,
  data: {
    levelInfo: CefrLevel;
    name: string;
    formattedDate: string;
    qrCodeContent: string;
    result?: LessonResult;
    level?: string;
  }
): string {
  const {levelInfo, name, formattedDate, qrCodeContent, result, level} = data;

  let processedTemplate = template;

  // Basic replacements
  const replacements = {
    "\\[LEVEL\\]": levelInfo.level,
    "\\[LEVEL_NAME\\]": levelInfo.levelName,
    "\\[FULL_NAME\\]": name,
    "\\[DATE\\]": formattedDate,
    "\\[WHAT_YOU_ACHIVE\\]": levelInfo.whatYouAchieve,
    "\\[A1\\] Level": `${levelInfo.level} Level`,
    "\\[QRCODE\\]": qrCodeContent,
    "\\[LEVEL_DESCRIPTION\\]": levelInfo.description,
  };

  // Apply basic replacements
  for (const [pattern, replacement] of Object.entries(replacements)) {
    processedTemplate = processedTemplate.replace(
      new RegExp(pattern, "g"),
      replacement
    );
  }

  // Handle wrapped text for "what you can do"
  const wrappedCanDo = wrapText(levelInfo.whatYouCanDo);
  const canDoTspans = wrappedCanDo
    .map(
      (line, index) => `<tspan x="199" y="${215 + index * 8}">${line}</tspan>`
    )
    .join("");
  processedTemplate = processedTemplate.replace(
    /\[WHAT_YOU_CAN_DO\]/g,
    canDoTspans
  );

  // Handle scores and predicates if result is provided
  if (result && level) {
    const roundedPronunciation = Math.round(result.pronunciation);
    const roundedListening = Math.round(result.listening);
    const roundedSpeaking = Math.round(result.speaking);

    const scoreReplacements = {
      "\\[PRONUNCIATION_SCORE\\]": roundedPronunciation.toString(),
      "\\[LISTENING_SCORE\\]": roundedListening.toString(),
      "\\[SPEAKING_SCORE\\]": roundedSpeaking.toString(),
      "\\[PRONUNCIATION_PREDICATE\\]": getPredicateForScore(
        roundedPronunciation,
        level
      ),
      "\\[LISTENING_PREDICATE\\]": getPredicateForScore(
        roundedListening,
        level
      ),
      "\\[SPEAKING_PREDICATE\\]": getPredicateForScore(roundedSpeaking, level),
    };

    for (const [pattern, replacement] of Object.entries(scoreReplacements)) {
      processedTemplate = processedTemplate.replace(
        new RegExp(pattern, "g"),
        replacement
      );
    }
  }

  return processedTemplate;
}

export const generateCertificate = onDocumentWritten(
  {
    document: "user-data/{userId}/lessons/{levelId}",
    region: "asia-southeast1",
  },
  async (event) => {
    const beforeData = event.data?.before.data();
    const afterData = event.data?.after.data();

    // Generate certificate when a level is completed with sufficient scores
    if (beforeData?.isComplete || !afterData?.isComplete) {
      logger.log("No change to completion status, exiting.");
      return;
    }

    const {userId, levelId} = event.params;
    const level = levelId.substring(0, 2).toUpperCase();

    if (!cefrLevels[level]) {
      logger.error(`Invalid level derived from levelId: ${levelId}`);
      return;
    }

    // Validate user scores before generating certificate
    const result = afterData?.result as LessonResult | undefined;
    if (!validateScores(result)) {
      logger.log(
        `Certificate generation skipped for ${userId} level ${level}: ` +
          "insufficient scores (minimum 50 required for pronunciation, " +
          "listening, and speaking)"
      );
      return;
    }

    const currentAttemptId = afterData?.attemptId;
    logger.log(
      `Certificate generation proceeding for ${userId} level ${level}: ` +
        `attemptId=${currentAttemptId || "undefined"}`
    );

    const auth = getAuth();
    let name: string;

    try {
      const userRecord = await auth.getUser(userId);
      name = userRecord.displayName || userRecord.email || "Unknown User";

      if (!userRecord.displayName && !userRecord.email) {
        logger.error(
          `User display name and email not found for userId: ${userId}`
        );
        return;
      }
    } catch (error) {
      logger.error(
        `Failed to get user data from Firebase Auth for userId: ${userId}`,
        error
      );
      return;
    }

    const levelInfo = cefrLevels[level];
    const currentDate = new Date();
    const day = currentDate.getDate().toString().padStart(2, "0");
    const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
    const year = currentDate.getFullYear();
    const formattedDate = `${day}/${month}/${year}`;
    const certificateId = ulid();

    // Generate QR code with verification URL
    const qrCodeUrl = `https://verify.selfeng.id/?userid=${userId}&certid=${certificateId}`;
    const qrCode = new QRCode({
      content: qrCodeUrl,
      width: 30,
      height: 30,
      color: "#000000",
      background: "#ffffff",
      padding: 0,
    });

    // Extract QR code content without XML declaration and root SVG tags
    const qrCodeSvg = qrCode.svg();
    const qrCodeContent = qrCodeSvg
      .replace(/<\?xml[^>]*\?>/, "")
      .replace(/<svg[^>]*>/, "")
      .replace(/<\/svg>$/, "")
      .trim();

    // Process certificate templates
    const templateData = {
      levelInfo,
      name,
      formattedDate,
      qrCodeContent,
      result,
      level,
    };

    const certificatePageOne = processCertificateTemplate(
      certificateTemplate,
      templateData
    );
    const certificatePageTwo = processCertificateTemplate(
      certificateTemplatePageTwo,
      templateData
    );

    // Define file paths
    const fileNamePageOne = `${certificateId}-01.png`;
    const fileNamePageTwo = `${certificateId}-02.png`;
    const tempFilePathPageOne = path.join(os.tmpdir(), fileNamePageOne);
    const tempFilePathPageTwo = path.join(os.tmpdir(), fileNamePageTwo);

    try {
      // Convert SVG to PNG buffers in parallel for performance
      logger.log(`Starting PNG conversion for certificate ${certificateId}`);

      const [pngBufferPageOne, pngBufferPageTwo] = await Promise.all([
        convertSvgToPng(certificatePageOne, 1200, 900, 85),
        convertSvgToPng(certificatePageTwo, 1200, 900, 85),
      ]);

      logger.log(`PNG conversion completed for certificate ${certificateId}`);

      // Write PNG buffers to temporary files
      await Promise.all([
        fs.writeFile(tempFilePathPageOne, pngBufferPageOne),
        fs.writeFile(tempFilePathPageTwo, pngBufferPageTwo),
      ]);

      const bucket = getStorage().bucket();
      const destinationPageOne =
        `certificates/${userId}/${level}/${fileNamePageOne}`;
      const destinationPageTwo =
        `certificates/${userId}/${level}/${fileNamePageTwo}`;

      // Upload both files in parallel
      const [filePageOne, filePageTwo] = await Promise.all([
        bucket.upload(tempFilePathPageOne, {
          destination: destinationPageOne,
          public: true,
          metadata: {
            contentType: "image/png",
            cacheControl: "public, max-age=31536000", // Cache for 1 year
            metadata: {
              certificateId,
              userId,
              level,
              generatedAt: new Date().toISOString(),
            },
          },
        }),
        bucket.upload(tempFilePathPageTwo, {
          destination: destinationPageTwo,
          public: true,
          metadata: {
            contentType: "image/png",
            cacheControl: "public, max-age=31536000",
            metadata: {
              certificateId,
              userId,
              level,
              generatedAt: new Date().toISOString(),
            },
          },
        }),
      ]);

      const storagePathPageOne = filePageOne[0].publicUrl();
      const storagePathPageTwo = filePageTwo[0].publicUrl();

      // Prepare certificate data
      const roundedPronunciation = Math.round(result!.pronunciation);
      const roundedListening = Math.round(result!.listening);
      const roundedSpeaking = Math.round(result!.speaking);

      const certificateData: CertificateData = {
        certificateId: certificateId,
        certificateUrlPageOne: storagePathPageOne,
        certificateUrlPageTwo: storagePathPageTwo,
        userId: userId,
        level: level,
        generatedAt: new Date().toISOString(),
        scores: {
          pronunciation: roundedPronunciation,
          listening: roundedListening,
          speaking: roundedSpeaking,
        },
        predicates: {
          pronunciation: getPredicateForScore(roundedPronunciation, level),
          listening: getPredicateForScore(roundedListening, level),
          speaking: getPredicateForScore(roundedSpeaking, level),
        },
      };

      if (currentAttemptId !== undefined) {
        certificateData.attemptId = currentAttemptId;
      }

      await event.data?.after.ref.set(certificateData, {merge: true});

      logger.log(
        `Certificate ${certificateId} for ${userId} for level ${level} ` +
          `created - Page 1: ${storagePathPageOne}, ` +
          `Page 2: ${storagePathPageTwo}`
      );
    } catch (error) {
      logger.error(
        `Failed to generate certificate for ${userId} for level ${level}`,
        error
      );
    } finally {
      // Clean up temporary files in parallel
      await Promise.allSettled([
        fs.unlink(tempFilePathPageOne),
        fs.unlink(tempFilePathPageTwo),
      ]);
    }
  }
);
