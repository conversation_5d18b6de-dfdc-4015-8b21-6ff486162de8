/* eslint-disable require-jsdoc */
import {onDocumentWritten} from "firebase-functions/v2/firestore";
import {getFirestore, FieldValue} from "firebase-admin/firestore";
import {logger} from "firebase-functions/v2";
import {LessonResult} from "./interface/LessonResult";

async function calculateResult(
  userId: string,
  levelId: string,
): Promise<LessonResult> {
  const db = getFirestore();

  // Get all chapters in the level - only fetch completed chapters with results
  const chaptersSnapshot = await db.collection(
    `user-data/${userId}/lessons/${levelId}/chapters`
  )
    .where("isComplete", "==", true)
    .where("result", "!=", null)
    .get();

  if (chaptersSnapshot.empty) {
    return {
      pronunciation: 0,
      listening: 0,
      speaking: 0,
    };
  }

  // Process results more efficiently using a single pass
  const keyTotals: {
    [key in keyof LessonResult]: {sum: number; count: number}
  } = {
    pronunciation: {sum: 0, count: 0},
    listening: {sum: 0, count: 0},
    speaking: {sum: 0, count: 0},
  };

  chaptersSnapshot.docs.forEach((doc) => {
    const result = doc.data().result as LessonResult;
    if (result && typeof result === "object") {
      (Object.keys(keyTotals) as Array<keyof LessonResult>).forEach((key) => {
        const value = result[key];
        if (typeof value === "number") {
          keyTotals[key].sum += value;
          keyTotals[key].count += 1;
        }
      });
    }
  });

  // Calculate averages in a single pass
  const averageResults: LessonResult = {
    pronunciation: keyTotals.pronunciation.count > 0 ?
      keyTotals.pronunciation.sum / keyTotals.pronunciation.count : 0,
    listening: keyTotals.listening.count > 0 ?
      keyTotals.listening.sum / keyTotals.listening.count : 0,
    speaking: keyTotals.speaking.count > 0 ?
      keyTotals.speaking.sum / keyTotals.speaking.count : 0,
  };

  return averageResults;
}

async function setComplete(
  userId: string,
  levelId: string,
): Promise<void> {
  const db = getFirestore();

  // Calculate the average results for the level and update in parallel
  const [results] = await Promise.all([
    calculateResult(userId, levelId),
  ]);

  await db.doc(`user-data/${userId}/lessons/${levelId}`).set(
    {
      isComplete: true,
      result: results,
      updatedAt: FieldValue.serverTimestamp(),
    },
    {merge: true},
  );
}

export const setLevelComplete = onDocumentWritten(
  {
    document: "user-data/{userId}/lessons/{levelId}/chapters/{chId}",
    region: "asia-southeast1",
  },
  async (event) => {
    // Early exit checks
    if (!event.data?.after.exists) {
      return;
    }

    const chapterData = event.data.after.data();
    if (!chapterData?.isComplete) {
      return;
    }

    const {userId, levelId} = event.params;
    const db = getFirestore();

    // Check if level is already complete to avoid redundant processing
    const levelDoc = await db.doc(`user-data/${userId}/lessons/${levelId}`)
      .get();

    if (levelDoc.exists && levelDoc.data()?.isComplete) {
      return; // Level already marked as complete
    }

    // Determine required chapters based on level prefix
    const levelIdPrefix = levelId.substring(0, 2).toUpperCase();
    let requiredChapters: number;

    if (levelIdPrefix === "C2") {
      requiredChapters = 7;
    } else if (["A1", "A2", "B1", "B2", "C1"].includes(levelIdPrefix)) {
      requiredChapters = 8;
    } else {
      logger.error(`Unknown level type for levelId: ${levelId}`);
      return;
    }

    // Count completed chapters efficiently
    const completedChaptersSnapshot = await db.collection(
      `user-data/${userId}/lessons/${levelId}/chapters`
    )
      .where("isComplete", "==", true)
      .select() // Only get document IDs for counting, not full data
      .get();

    // Only proceed if all required chapters are complete
    if (completedChaptersSnapshot.size === requiredChapters) {
      await setComplete(userId, levelId);
    }
  },
);
