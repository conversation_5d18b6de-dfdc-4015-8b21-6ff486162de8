---
description: Repository Information Overview
alwaysApply: true
---

# Firebase Functions Project Information

## Summary
This project is a Firebase Cloud Functions application that provides backend functionality for a language learning application. It includes features for user data management, pronunciation checking, and certificate generation.

## Structure
- **functions/**: Contains the Firebase Cloud Functions code
  - **src/**: TypeScript source code
  - **lib/**: Compiled JavaScript output
  - **class/**: Class definitions
  - **helper/**: Helper utilities
  - **interface/**: TypeScript interfaces
  - **template/**: Templates for certificate generation

## Language & Runtime
**Language**: TypeScript
**Version**: ES2017 target
**Node Version**: 20
**Build System**: TypeScript compiler
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- firebase-admin: ^13.2.0
- firebase-functions: ^6.1.0
- microsoft-cognitiveservices-speech-sdk: ^1.44.1
- @azure/identity: ^4.10.1
- busboy: ^1.6.0

**Development Dependencies**:
- typescript: ^4.9.0
- eslint: ^8.9.0
- @typescript-eslint/eslint-plugin: ^5.12.0
- @typescript-eslint/parser: ^5.12.0
- firebase-functions-test: ^3.1.0

## Build & Installation
```bash
# Install dependencies
cd functions
npm install

# Build the project
npm run build

# Deploy to Firebase
npm run deploy

# Run locally with emulators
npm run serve
```

## Firebase Configuration
**Configuration File**: firebase.json
**Emulators**:
- Auth: Port 9099
- Functions: Port 5001
- Firestore: Port 8080
- UI: Enabled

## Main Functions
The project exports several Firebase Cloud Functions:
- **initUserData**: Initialize user data
- **reinitUserData**: Reinitialize user data
- **checkPronunciationViaBuffer**: Check pronunciation using buffer files
- **chapterComplete**: Mark a chapter as complete
- **levelComplete**: Mark a level as complete
- **genCertificate**: Generate certificates for users

## Testing
**Framework**: firebase-functions-test
**Run Command**:
```bash
cd functions
npm run lint
```